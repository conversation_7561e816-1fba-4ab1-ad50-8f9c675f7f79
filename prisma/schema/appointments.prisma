model cities {
  id        BigInt   @id @default(autoincrement())
  city_name String
  state_name String?
  clinics   clinics[]
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @default(now()) @db.Timestamptz(6)

  @@map("cities")
}

model clinics {
  id           BigInt   @id @default(autoincrement())
  clinic_name  String
  address      String?
  city_id      BigInt?
  contact_info String?
  latitude     Float?
  longitude    Float?
  city         cities?  @relation(fields: [city_id], references: [id])
  doctors      doctors[]
  appointments appointments[]
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @db.Timestamptz(6)

  @@map("clinics")
}

model doctors {
  id                 BigInt   @id
  specialization_name String?
  profile            profiles @relation("DoctorProfile", fields: [id], references: [id])
  clinic             clinics? @relation(fields: [clinic_id], references: [id])
  clinic_id          BigInt?
  appointments       appointments[]
  time_slots         time_slots[]
  created_at         DateTime @default(now()) @db.Timestamptz(6)
  updated_at         DateTime @default(now()) @db.Timestamptz(6)

  @@map("doctors")
}

model patients {
  id          BigInt   @id
  profile     profiles @relation("PatientProfile", fields: [id], references: [id])
  appointments appointments[]
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @default(now()) @db.Timestamptz(6)

  @@map("patients")
}

model appointments {
  id                BigInt           @id @default(autoincrement())
  doctor_id         BigInt
  patient_id        BigInt?
  clinic_id         BigInt
  appointment_date  DateTime
  start_time        DateTime
  end_time          DateTime
  consultation_type ConsultationType
  status            AppointmentStatus
  duration          Int
  fees              Decimal          @db.Decimal(10, 2)
  currency          String
  payment_status    PaymentStatus
  booking_date      DateTime?
  doctor            doctors          @relation(fields: [doctor_id], references: [id])
  patient           patients?        @relation(fields: [patient_id], references: [id])
  clinic            clinics          @relation(fields: [clinic_id], references: [id])
  created_at        DateTime         @default(now()) @db.Timestamptz(6)
  updated_at        DateTime         @default(now()) @db.Timestamptz(6)

  @@map("appointments")
}

model time_slots {
  id           BigInt   @id @default(autoincrement())
  doctor_id    BigInt
  date         DateTime
  start_time   DateTime
  end_time     DateTime
  duration     Int      @default(15)
  is_available Boolean  @default(true)
  fee          Decimal? @db.Decimal(10, 2)
  currency     String?
  doctor       doctors  @relation(fields: [doctor_id], references: [id])
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @db.Timestamptz(6)

  @@map("time_slots")
}

enum ConsultationType {
  in_person
  online
}

enum AppointmentStatus {
  completed
  upcoming
  cancelled
}

enum PaymentStatus {
  paid
  unpaid
}
