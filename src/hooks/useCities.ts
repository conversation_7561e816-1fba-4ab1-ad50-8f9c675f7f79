import { useMemo } from "react";
import { appointmentCenters } from "@/components/shared/Appointments/appointmentModuleData";

export interface City {
  cityId: number;
  city: string;
}

export const useCities = (): City[] => {
  const cities = useMemo(() => {
    return appointmentCenters.map((center) => ({
      cityId: center.cityId,
      city: center.city,
    }));
  }, []);

  return cities;
};

export default useCities;
