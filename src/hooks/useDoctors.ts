import { useMemo } from "react";
import { doctorsList } from "@/components/shared/Appointments/appointmentModuleData";

export interface Doctor {
  doctorId: string;
  name: string;
  imageUrl?: string;
  specialization: string;
  experience: string;
  clinicAddress: string;
  consultationMode: string;
  bio?: string;
}

export const useDoctors = (centerId: number): Doctor[] => {
  const doctors = useMemo(() => {
    // Filter doctors by centerId
    const centerDoctors = doctorsList.filter(
      (doctor) => doctor.centerId === centerId
    );

    if (!centerDoctors.length) {
      return [];
    }

    return centerDoctors.map((doctor) => ({
      doctorId: doctor.doctorId,
      name: doctor.name,
      imageUrl: doctor.imageUrl,
      specialization: doctor.specialization,
      experience: doctor.experience,
      clinicAddress: doctor.clinicAddress,
      consultationMode: doctor.consultationMode,
      bio: doctor.bio,
    }));
  }, [centerId]);

  return doctors;
};

export default useDoctors;
