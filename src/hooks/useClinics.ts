import { useMemo } from "react";
import { appointmentCenters } from "@/components/shared/Appointments/appointmentModuleData";

export interface Clinic {
  centerId: number;
  name: string;
}

export const useClinics = (cityId: number): Clinic[] => {
  const clinics = useMemo(() => {
    const cityData = appointmentCenters.find(
      (center) => center.cityId === cityId
    );

    if (!cityData) {
      return [];
    }

    return cityData.centers.list.map((clinic) => ({
      centerId: clinic.centerId,
      name: clinic.name,
    }));
  }, [cityId]);

  return clinics;
};

export default useClinics;
