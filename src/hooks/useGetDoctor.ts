import { useMemo } from "react";
import { doctorsList } from "@/components/shared/Appointments/appointmentModuleData";

export interface DoctorDetails {
  doctorId: string;
  centerId: number;
  name: string;
  imageUrl?: string;
  specialization: string;
  experience: string;
  clinicAddress: string;
  consultationMode: string;
  bio?: string;
}

export const useGetDoctor = (doctorId: string): DoctorDetails | null => {
  const doctor = useMemo(() => {
    // Find the doctor by doctorId
    const foundDoctor = doctorsList.find(
      (doctor) => doctor.doctorId === doctorId
    );

    if (foundDoctor) {
      return {
        doctorId: foundDoctor.doctorId,
        centerId: foundDoctor.centerId,
        name: foundDoctor.name,
        imageUrl: foundDoctor.imageUrl,
        specialization: foundDoctor.specialization,
        experience: foundDoctor.experience,
        clinicAddress: foundDoctor.clinicAddress,
        consultationMode: foundDoctor.consultationMode,
        bio: foundDoctor.bio,
      };
    }

    return null;
  }, [doctorId]);

  return doctor;
};

export default useGetDoctor;
