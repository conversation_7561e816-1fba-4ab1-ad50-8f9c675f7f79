import { useMemo } from "react";
import { appointmentCenters } from "@/components/shared/Appointments/appointmentModuleData";

export const useGetClinic = (centerId: number): string => {
  const clinic = useMemo(() => {
    // Find the center across all cities
    for (const cityData of appointmentCenters) {
      const center = cityData.centers.list.find(
        (center) => center.centerId === centerId
      );
      if (center) {
        return center;
      }
    }
    return null;
  }, [centerId]);

  return clinic?.name || "";
};

export default useGetClinic;
