import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Header, { HeaderState } from "@/components/shared/Header/Header";
import Footer from "@/components/shared/Footer/Footer";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import NeedHelp from "@/components/shared/NeedHelp/NeedHelp";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import OTPInput from "@/components/shared/OTPInput/OTPInput";
import { useCountdown } from "@/hooks/useCountdown";
import { setGuestSessionToken } from "@/utils/guestSessionUtils";

// Arrow right icon for the button
const ArrowRightIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M5 12h14" />
    <polyline points="12,5 19,12 12,19" />
  </svg>
);

const VerifyEmail: React.FC = () => {
  const [otp, setOtp] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const [sessionToken, setSessionToken] = useState("");
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [error, setError] = useState("");
  const [otpKey, setOtpKey] = useState(0); // Key to force OTP component re-render
  const [remainingAttempts, setRemainingAttempts] = useState<
    number | undefined
  >();
  const router = useRouter();

  // Initialize countdown timer with 2 minutes (120 seconds)
  const countdown = useCountdown(120);

  useEffect(() => {
    // Check for signup data in sessionStorage
    const signupData = sessionStorage.getItem("ivf_signup_data");
    if (signupData) {
      try {
        const parsedData = JSON.parse(signupData);
        setUserEmail(parsedData.email);
        setSessionToken(parsedData.session_token);
        setShowSuccessMessage(true);

        // Optionally clear the signup data after a delay
        setTimeout(() => {
          setShowSuccessMessage(false);
        }, 10000); // Hide message after 10 seconds
      } catch (error) {
        console.error("Error parsing signup data:", error);
      }
    }
  }, []);

  const clearOTPInput = () => {
    setOtp("");
    // Force re-render of OTPInput component by changing key
    setOtpKey((prev) => prev + 1);
  };

  const handleOTPComplete = async (otpValue: string) => {
    if (!userEmail || !sessionToken) {
      setError("Email or session not found. Please try signing up again.");
      return;
    }

    setIsVerifying(true);
    setError("");

    try {
      // Call our new guest-auth verify OTP API
      const verifyResponse = await fetch("/api/v1/guest-auth/verify-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_token: sessionToken,
          email: userEmail,
          code: otpValue,
        }),
      });

      const responseData = await verifyResponse.json();

      if (!verifyResponse.ok) {
        console.error("Verification error:", responseData);
        setError(
          responseData.message ||
            "Invalid verification code. Please check your code and try again."
        );
        clearOTPInput();

        // Set remaining attempts if provided
        if (responseData.data?.remainingAttempts !== undefined) {
          setRemainingAttempts(responseData.data.remainingAttempts);
        }
        return;
      }

      if (responseData.verified === true) {
        // Store session token from response for guest results
        const responseSessionToken = responseData.session_token;
        setGuestSessionToken(responseSessionToken);

        sessionStorage.removeItem("Test_status");
        sessionStorage.setItem("Result_status", "completed");

        // Clear the signup data from sessionStorage
        sessionStorage.removeItem("ivf_signup_data");

        // Redirect to results page immediately
        router.push("/ivf-assessment/results");
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error("Verification error:", err);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleOTPChange = (otpValue: string) => {
    setOtp(otpValue);
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleResendCode = async () => {
    if (!userEmail || !sessionToken) {
      setError("Email or session not found. Please try signing up again.");
      return;
    }

    setIsResending(true);
    setError("");

    try {
      // Call our new guest-auth signup API again to resend OTP
      const resendResponse = await fetch("/api/v1/guest-auth/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_token: sessionToken,
          email: userEmail,
          selected_track: "1", // Default track
        }),
      });

      if (!resendResponse.ok) {
        const errorData = await resendResponse.json();
        console.error("Resend error:", errorData);
        setError(
          errorData.message ||
            "Failed to resend verification code. Please try again."
        );
      } else {
        const responseData = await resendResponse.json();
        console.log("Verification code resent to:", userEmail);

        // Clear the OTP input fields
        clearOTPInput();

        // Reset the countdown timer when code is resent successfully
        countdown.reset(120); // Reset to 2 minutes

        // Optionally show a success message
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);

        // Update sessionStorage with new development code if available
        if (responseData.data?.developmentCode) {
          const existingData = sessionStorage.getItem("ivf_signup_data");
          if (existingData) {
            const parsedData = JSON.parse(existingData);
            parsedData.developmentCode = responseData.data.developmentCode;
            sessionStorage.setItem(
              "ivf_signup_data",
              JSON.stringify(parsedData)
            );
          }
        }
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error("Resend error:", err);
    } finally {
      setIsResending(false);
    }
  };

  const handleViewMyScore = async () => {
    if (otp.length === 6) {
      await handleOTPComplete(otp);
    }
  };

  // Use actual email if available, otherwise fallback to masked email
  const displayEmail = userEmail || "j*****@g***.com";

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full px-4">
          <StepHeader currentStep={5} totalSteps={5} title="Verify Email" />

          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-8 text-center">
              {/* Success Message */}
              {showSuccessMessage && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4 text-left">
                  <div className="text-green-800 text-sm">
                    ✓ Verification code sent successfully! Please check your
                    email.
                  </div>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 text-left">
                  <div className="text-red-800 text-sm">{error}</div>
                  {remainingAttempts !== undefined && (
                    <div className="text-red-600 text-xs mt-1">
                      Remaining attempts: {remainingAttempts}
                    </div>
                  )}
                </div>
              )}

              {/* Instructions */}
              <div className="space-y-1">
                <p className="text-[var(--grey-6)] text-base">
                  We&apos;ve sent you a passcode.
                </p>
                <p className="text-[var(--grey-6)] text-base">
                  Please check your inbox at {displayEmail}.
                </p>
              </div>

              {/* OTP Input */}
              <div className="flex justify-center">
                <OTPInput
                  key={otpKey} // This forces a complete re-render and reset
                  length={6}
                  onChange={handleOTPChange}
                  onComplete={handleOTPComplete}
                  autoFocus={true}
                />
              </div>

              {/* Resend Code Link with Countdown Timer */}
              <div className="flex items-center justify-center gap-1">
                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={isResending || !countdown.isExpired}
                  className="text-[var(--grey-5)] text-base hover:text-[var(--grey-6)] transition-colors duration-200 underline disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isResending ? "Resending..." : "Resend Code"}
                </button>

                {/* Countdown Timer Display */}
                {!countdown.isExpired && (
                  <p className="text-[var(--grey-5)] text-base">
                    in{" "}
                    <span className="text-[var(--violet-11)] text-base font-bold">
                      ({countdown.formattedTime})
                    </span>
                  </p>
                )}
              </div>

              {/* Additional info when timer is active */}
              {!countdown.isExpired && (
                <p className="text-[var(--red-6)] text-base">
                  Please do not refresh or close this page.
                </p>
              )}

              {/* View My Score Button */}
              <div className="mt-15">
                <Button
                  type={ButtonType.PRIMARY}
                  text={isVerifying ? "Verifying..." : "View My Score"}
                  icon={!isVerifying ? <ArrowRightIcon /> : undefined}
                  onClick={handleViewMyScore}
                  disabled={otp.length !== 6 || isVerifying}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </main>
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};

export default VerifyEmail;
