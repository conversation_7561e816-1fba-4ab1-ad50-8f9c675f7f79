/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from "react";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import Input from "../../shared/Input/Input";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import StepHeader from "../../shared/StepHeader/StepHeader";
import Link from "next/link";
import { WarningCircleIcon } from "@phosphor-icons/react";
import { getGuestSessionToken } from "@/utils/guestSessionUtils";
import { usePathname } from "next/navigation";
// Arrow right icon for the verify button
const ArrowRightIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M5 12h14" />
    <polyline points="12,5 19,12 12,19" />
  </svg>
);
export interface SignupProps {
  onVerifyEmail?: () => void;
}
const Signup: React.FC<SignupProps> = ({ onVerifyEmail }) => {
  const pathname = usePathname();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [number, setNumber] = useState("");
  const [consent, setConsent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isAuthEmail, setIsAuthEmail] = useState(false);
  // Basic form validation
  const isFormValid = () => {
    return (
      name.trim() !== "" &&
      email.trim() !== "" &&
      email.includes("@") &&
      consent
    );
  };
  const onSignup = async () => {
    if (!isFormValid()) {
      setError("Please fill in all required fields and accept the consent");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Get guest session token from storage
      const guestSessionToken = getGuestSessionToken();

      if (!guestSessionToken) {
        setError(
          "Guest session not found. Please complete the IVF assessment steps first."
        );
        return;
      }

      const selected_track = sessionStorage.getItem(
        "ivf_selected_assessment_type"
      );

      // Call our new guest-auth signup API
      const signupResponse = await fetch("/api/v1/guest-auth/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_token: guestSessionToken,
          email: email.trim(),
          display_name: name.trim(),
          phone: number.trim() || undefined,
          selected_track,
        }),
      });

      if (signupResponse.status === 409) {
        setIsAuthEmail(true);
        return;
      }

      if (!signupResponse.ok) {
        const errorData = await signupResponse.json();
        console.error("API error:", errorData);
        setError(errorData.message || "Error signing up. Please try again.");
        return;
      }

      const responseData = await signupResponse.json();

      // Store signup data for the verification step
      sessionStorage.setItem(
        "ivf_signup_data",
        JSON.stringify({
          name: name.trim(),
          email: email.trim(),
          phone: number.trim(),
          consent,
          session_token: guestSessionToken,
          // Include development code for testing
          ...(responseData.data?.developmentCode && {
            developmentCode: responseData.data.developmentCode,
          }),
        })
      );

      // Success - OTP has been sent
      console.log("OTP sent successfully to:", email.trim());

      // Redirect to verify email page
      if (onVerifyEmail) {
        onVerifyEmail();
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error("Signup error:", err);
    } finally {
      setLoading(false);
    }
  };
  const handleConsentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConsent(e.target.checked);
    // Clear error when user interacts with form
    if (error) setError("");
  };
  const handleInputChange =
    (field: "name" | "email" | "number") => (value: string) => {
      if (field === "name") setName(value);
      if (field === "email") setEmail(value);
      if (field === "number") setNumber(value);
      // Clear error when user starts typing
      if (error) setError("");
    };
  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader
            currentStep={4}
            totalSteps={5}
            title="Signup with Email"
          />
          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-6 flex flex-col gap-[4.625rem] p-6">
              {/* Error Message */}
              {error && (
                <div className="bg-[var(--error-red-1)] border border-[var(--error-red-2)] rounded-md p-4">
                  <div className="text-[var(--error-red-4)] text-sm">
                    {error}
                  </div>
                </div>
              )}
              {/* Form Fields */}
              <div className="flex flex-col gap-4">
                <div>
                  <Input
                    type="text"
                    value={name}
                    onChange={handleInputChange("name")}
                    label="Name"
                    required={true}
                    placeholder="Enter your full name"
                    disabled={loading}
                    maxLength={50}
                  />
                </div>
                {/* Email Field */}
                <div>
                  <Input
                    type="email"
                    value={email}
                    onChange={handleInputChange("email")}
                    required={true}
                    label="Email"
                    placeholder="Enter your email"
                    disabled={loading}
                    maxLength={50}
                  />
                </div>
                {/* Number Field (Optional) */}
                <div>
                  <Input
                    type="tel"
                    value={number}
                    onChange={handleInputChange("number")}
                    label="Number (Optional)"
                    placeholder="Enter your phone number"
                    disabled={loading}
                    maxLength={15}
                  />
                </div>
                {/* Consent Checkbox */}
                <div className="flex items-start gap-3 mt-6">
                  <input
                    type="checkbox"
                    id="consent"
                    checked={consent}
                    onChange={handleConsentChange}
                    disabled={loading}
                    className="mt-1 h-4 w-4 text-[var(--violet-6)] focus:ring-[var(--violet-6)] border-[var(--grey-3)] rounded disabled:opacity-50"
                  />
                  <label
                    htmlFor="consent"
                    className="text-[var(--grey-6)] text-base leading-relaxed cursor-pointer"
                  >
                    I consent to share my inputs for consultation purposes *
                  </label>
                </div>
              </div>
              {isAuthEmail && (
                <div className="flex gap-1 items-start border border-[var(--info-blue-2)] rounded-md p-4 bg-[var(--info-blue-1)]">
                  <WarningCircleIcon
                    size={20}
                    weight="bold"
                    className="text-[var(--violet-11)]"
                  />
                  <p className="text-[var(--grey-6)] text-sm text-center">
                    This email is already associated with an account. Please{" "}
                    <Link
                      href={`/login?from=${pathname === "/ivf-assessment/signup" ? "signup" : "verify-details"}`}
                      className="text-[var(--red-6)] font-bold underline"
                    >
                      Login
                    </Link>{" "}
                    instead.
                  </p>
                </div>
              )}
              {/* Verify Email Button */}
              <Button
                type={ButtonType.PRIMARY}
                text={loading ? "Creating Account..." : "Verify Email"}
                icon={!loading ? <ArrowRightIcon /> : undefined}
                onClick={onSignup}
                disabled={!isFormValid() || loading}
                className="w-full"
              />
              {/* Help Text */}
              <div className="text-center">
                <p className="text-[var(--grey-5)] text-sm">
                  By signing up, you&apos;ll receive a verification email to
                  confirm your account.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};
export default Signup;
