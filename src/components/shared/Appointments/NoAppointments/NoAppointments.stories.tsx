import React from "react";
import { Meta, StoryObj } from "@storybook/react";
import NoAppointments from "./NoAppointments";

const meta: Meta<typeof NoAppointments> = {
  title: "Components/Appointments/NoAppointments",
  component: NoAppointments,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    onScheduleClick: { action: "schedule clicked" },
  },
};

export default meta;

type Story = StoryObj<typeof NoAppointments>;

export const Default: Story = {
  args: {
    onScheduleClick: () => console.log("Schedule appointment clicked"),
  },
};

export const WithCustomClassName: Story = {
  args: {
    onScheduleClick: () => console.log("Schedule appointment clicked"),
    className: "bg-[var(--grey-1)] min-h-screen",
  },
};

export const WithoutClickHandler: Story = {
  args: {},
};
