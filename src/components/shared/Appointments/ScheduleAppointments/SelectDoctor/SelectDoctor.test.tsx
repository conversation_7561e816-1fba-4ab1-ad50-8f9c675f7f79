import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import SelectDoctor from "./SelectDoctor";

// Mock the useDoctors hook
jest.mock("@/hooks/useDoctors", () => ({
  useDoctors: (centerId: number) => {
    const doctorsData = {
      11: [
        {
          doctorId: "11-a",
          centerId: 11,
          name: "Dr. <PERSON><PERSON><PERSON>",
          specialization: "Fertility Consultant, Obstetrician",
          experience: "9+ years",
          clinicAddress: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
          consultationMode: "In-Person Consultation",
        },
        {
          doctorId: "11-b",
          centerId: 11,
          name: "Dr. <PERSON><PERSON>",
          specialization: "IVF & IUI Expert",
          experience: "10+ years",
          clinicAddress: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
          consultationMode: "In-Person Consultation",
        },
        {
          doctorId: "11-c",
          centerId: 11,
          name: "Dr. <PERSON><PERSON><PERSON>",
          specialization: "Reproductive Endocrinologist",
          experience: "14+ years",
          clinicAddress: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
          consultationMode: "In-Person Consultation",
        },
        {
          doctorId: "11-d",
          centerId: 11,
          name: "Dr. Priya Sharma",
          specialization: "IVF Specialist, Gynaecologist",
          experience: "12+ years",
          clinicAddress: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
          consultationMode: "In-Person Consultation",
        },
      ],
      12: [
        {
          doctorId: "12-a",
          centerId: 12,
          name: "Dr. Aarti Mehra",
          specialization: "Fertility Consultant, Obstetrician",
          experience: "9+ years",
          clinicAddress: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
          consultationMode: "In-Person Consultation",
        },
        {
          doctorId: "12-b",
          centerId: 12,
          name: "Dr. Naresh Mehta",
          specialization: "IVF & IUI Expert",
          experience: "10+ years",
          clinicAddress: "Gunjan IVF Clinic, Indrapuram Ghaziabad",
          consultationMode: "In-Person Consultation",
        },
      ],
    };
    return doctorsData[centerId] || [];
  },
}));

// Mock the StepHeader component
jest.mock("@/components/shared/StepHeader/StepHeader", () => {
  return function MockStepHeader({ title }: { title: string }) {
    return <div data-testid="step-header">{title}</div>;
  };
});

// Mock the Button component
jest.mock("@/components/shared/Button/Button", () => {
  const MockButton = ({
    text,
    onClick,
  }: {
    text: string;
    onClick: () => void;
  }) => {
    return (
      <button data-testid="back-button" onClick={onClick}>
        {text}
      </button>
    );
  };

  MockButton.ButtonType = {
    PRIMARY: "primary",
    SECONDARY: "secondary",
  };

  return MockButton;
});

// Mock the DoctorCard component
jest.mock("@/components/shared/DoctorCard/DoctorCard", () => {
  return function MockDoctorCard({
    name,
    title,
    experience,
    selected,
    onClick,
  }: {
    name: string;
    title: string;
    experience: string;
    selected: boolean;
    onClick: () => void;
  }) {
    return (
      <div
        data-testid={`doctor-card-${name}`}
        data-selected={selected}
        onClick={onClick}
        className="doctor-card"
      >
        <div data-testid="doctor-name">{name}</div>
        <div data-testid="doctor-specialization">{title}</div>
        <div data-testid="doctor-experience">{experience}</div>
      </div>
    );
  };
});

describe("SelectDoctor", () => {
  const mockOnDoctorSelect = jest.fn();
  const mockOnBack = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with correct title and subtitle", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId("step-header")).toHaveTextContent(
      "Select Doctor"
    );
    expect(
      screen.getByText("Choose your preferred doctor to consult in clinic")
    ).toBeInTheDocument();
  });

  it("renders all doctors from the useDoctors hook for center 11", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    expect(
      screen.getByTestId("doctor-card-Dr. Aarti Mehra")
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("doctor-card-Dr. Naresh Mehta")
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("doctor-card-Dr. Neha Bansal")
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("doctor-card-Dr. Priya Sharma")
    ).toBeInTheDocument();
  });

  it("renders doctors for different center", () => {
    render(
      <SelectDoctor
        centerId={12}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    expect(
      screen.getByTestId("doctor-card-Dr. Aarti Mehra")
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("doctor-card-Dr. Naresh Mehta")
    ).toBeInTheDocument();

    // Should not render doctors from center 11
    expect(
      screen.queryByTestId("doctor-card-Dr. Neha Bansal")
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId("doctor-card-Dr. Priya Sharma")
    ).not.toBeInTheDocument();
  });

  it("renders back button", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId("back-button")).toBeInTheDocument();
    expect(screen.getByTestId("back-button")).toHaveTextContent("Back");
  });

  it("calls onDoctorSelect when a doctor is clicked", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    const doctorCard = screen.getByTestId("doctor-card-Dr. Aarti Mehra");
    fireEvent.click(doctorCard);

    expect(mockOnDoctorSelect).toHaveBeenCalledWith("11-a", "Dr. Aarti Mehra");
  });

  it("calls onBack when back button is clicked", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    const backButton = screen.getByTestId("back-button");
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalled();
  });

  it("shows selected doctor when selectedDoctorId is provided", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
        selectedDoctorId="11-a"
      />
    );

    const doctorCard = screen.getByTestId("doctor-card-Dr. Aarti Mehra");
    expect(doctorCard).toHaveAttribute("data-selected", "true");
  });

  it("shows no selected doctor when selectedDoctorId is not provided", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    const allDoctorCards = screen.getAllByTestId(/doctor-card-/);
    allDoctorCards.forEach((card) => {
      expect(card).toHaveAttribute("data-selected", "false");
    });
  });

  it("updates selected doctor when a different doctor is clicked", async () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
        selectedDoctorId="11-a"
      />
    );

    // Initially Dr. Aarti Mehra should be selected
    expect(screen.getByTestId("doctor-card-Dr. Aarti Mehra")).toHaveAttribute(
      "data-selected",
      "true"
    );

    // Click on Dr. Naresh Mehta
    const doctorCard = screen.getByTestId("doctor-card-Dr. Naresh Mehta");
    fireEvent.click(doctorCard);

    await waitFor(() => {
      expect(mockOnDoctorSelect).toHaveBeenCalledWith(
        "11-b",
        "Dr. Naresh Mehta"
      );
    });
  });

  it("handles multiple doctor selections correctly", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    // Click on first doctor
    fireEvent.click(screen.getByTestId("doctor-card-Dr. Aarti Mehra"));
    expect(mockOnDoctorSelect).toHaveBeenCalledWith("11-a", "Dr. Aarti Mehra");

    // Click on second doctor
    fireEvent.click(screen.getByTestId("doctor-card-Dr. Naresh Mehta"));
    expect(mockOnDoctorSelect).toHaveBeenCalledWith("11-b", "Dr. Naresh Mehta");

    // Verify both calls were made
    expect(mockOnDoctorSelect).toHaveBeenCalledTimes(2);
  });

  it("renders with correct accessibility attributes", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    // Check that all interactive elements are accessible
    expect(screen.getByTestId("back-button")).toBeInTheDocument();
    expect(screen.getAllByTestId(/doctor-card-/)).toHaveLength(4);
  });

  it("handles empty doctors list gracefully", () => {
    render(
      <SelectDoctor
        centerId={999} // Non-existent center ID
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    // Should still render the header and back button
    expect(screen.getByTestId("step-header")).toBeInTheDocument();
    expect(screen.getByTestId("back-button")).toBeInTheDocument();

    // Should not render any doctor cards
    expect(screen.queryAllByTestId(/doctor-card-/)).toHaveLength(0);
  });

  it("displays doctor information correctly", () => {
    render(
      <SelectDoctor
        centerId={11}
        onDoctorSelect={mockOnDoctorSelect}
        onBack={mockOnBack}
      />
    );

    // Check doctor names are displayed
    expect(screen.getByText("Dr. Aarti Mehra")).toBeInTheDocument();
    expect(screen.getByText("Dr. Naresh Mehta")).toBeInTheDocument();
    expect(screen.getByText("Dr. Neha Bansal")).toBeInTheDocument();
    expect(screen.getByText("Dr. Priya Sharma")).toBeInTheDocument();

    // Check specializations are displayed
    expect(
      screen.getByText("Fertility Consultant, Obstetrician")
    ).toBeInTheDocument();
    expect(screen.getByText("IVF & IUI Expert")).toBeInTheDocument();
    expect(
      screen.getByText("Reproductive Endocrinologist")
    ).toBeInTheDocument();
    expect(
      screen.getByText("IVF Specialist, Gynaecologist")
    ).toBeInTheDocument();

    // Check experience is displayed
    expect(screen.getByText("9+ years")).toBeInTheDocument();
    expect(screen.getByText("10+ years")).toBeInTheDocument();
    expect(screen.getByText("14+ years")).toBeInTheDocument();
    expect(screen.getByText("12+ years")).toBeInTheDocument();
  });
});
