import React, { useEffect, useState } from "react";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import ToggleButton from "@/components/shared/ToggleButton/ToggleButton";
import {
  ArrowLeftIcon,
  CaretDownIcon,
  CaretLeftIcon,
  CaretRightIcon,
} from "@phosphor-icons/react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

export interface SelectDateTimeProps {
  onDateTimeSelect: (date: string, time: string) => void;
  onBack: () => void;
  onCancel: () => void;
  onConfirm: () => void;
  selectedDate?: string;
  selectedTime?: string;
}

const SelectDateTime: React.FC<SelectDateTimeProps> = ({
  onDateTimeSelect,
  onBack,
  onCancel,
  onConfirm,
  selectedDate,
  selectedTime,
}) => {
  const [selectedDateState, setSelectedDateState] = useState<
    string | undefined
  >(selectedDate);
  const [selectedTimeState, setSelectedTimeState] = useState<
    string | undefined
  >(selectedTime);

  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle(null);
    setSubtitle(null);
    setBreadcrumbs([
      { label: "My Appointments", href: "/user/appointments" },
      { label: "Schedule Appointments", isActive: true },
    ]);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  // Generate dates for the next 7 days
  const generateDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        date: date.toISOString().split("T")[0],
        display: `${date.getDate()} ${date.toLocaleDateString("en-US", { month: "short" })}`,
        day: date.toLocaleDateString("en-US", { weekday: "short" }),
        isToday: i === 0,
      });
    }
    return dates;
  };

  // Available time slots
  const timeSlots = [
    "10:00 am",
    "10:30 am",
    "11:30 am",
    "12:00 pm",
    "12:30 pm",
    "03:00 pm",
    "03:30 pm",
    "04:30 pm",
    "05:00 pm",
    "05:30 pm",
    "06:30 pm",
    "07:00 am",
    "07:30 pm",
    "08:30 am",
  ];

  const dates = generateDates();

  const handleDateSelect = (date: string) => {
    setSelectedDateState(date);
    if (selectedTimeState) {
      onDateTimeSelect(date, selectedTimeState);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTimeState(time);
    if (selectedDateState) {
      onDateTimeSelect(selectedDateState, time);
    }
  };

  const handleTodayClick = () => {
    const today = new Date().toISOString().split("T")[0];
    handleDateSelect(today);
  };

  const isDateSelected = (date: string) => selectedDateState === date;
  const isTimeSelected = (time: string) => selectedTimeState === time;

  return (
    <div className="w-full flex flex-col items-center gap-8 md:gap-12">
      <div className="flex flex-col items-center gap-2">
        <StepHeader
          currentStep={4}
          totalSteps={4}
          title="Choose Date & Time"
          className="!mb-0"
        />

        <p className="text-center text-[var(--grey-6)] text-base font-medium">
          Choose your preferred date & time
        </p>
      </div>

      <div className="flex flex-col gap-8 xl:gap-12 w-full max-w-2xl">
        {/* Date Selection Section */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 text-[var(--grey-7)] text-base font-medium">
                <span>18 July, Tues</span>
                <CaretDownIcon size={16} />
              </div>
              <Button
                type={ButtonType.SECONDARY}
                text="Today"
                onClick={handleTodayClick}
                size="sm"
                className="!px-4 !py-2 !h-8"
              />
            </div>
            <div className="flex items-center gap-2">
              <button className="w-8 h-8 flex items-center justify-center border border-[var(--grey-3)] rounded-sm hover:bg-[var(--grey-2)]">
                <CaretLeftIcon size={16} />
              </button>
              <button className="w-8 h-8 flex items-center justify-center border border-[var(--grey-3)] rounded-sm hover:bg-[var(--grey-2)]">
                <CaretRightIcon size={16} />
              </button>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-[var(--grey-6)] text-base font-medium">
              What time best works for you
            </label>
            <div className="grid grid-cols-7 gap-2">
              {dates.map((dateInfo) => (
                <ToggleButton
                  key={dateInfo.date}
                  isSelected={isDateSelected(dateInfo.date)}
                  onClick={() => handleDateSelect(dateInfo.date)}
                  variant="compact"
                  className="!py-2 !px-3 text-sm"
                >
                  <div className="flex flex-col items-center gap-1">
                    <span className="text-xs">{dateInfo.day}</span>
                    <span>{dateInfo.display}</span>
                  </div>
                </ToggleButton>
              ))}
            </div>
          </div>
        </div>

        {/* Time Selection Section */}
        <div className="flex flex-col gap-4">
          <label className="text-[var(--grey-6)] text-base font-medium">
            Select time slot for you
          </label>
          <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
            {timeSlots.map((time) => (
              <ToggleButton
                key={time}
                isSelected={isTimeSelected(time)}
                onClick={() => handleTimeSelect(time)}
                variant="compact"
                className="!py-3 !px-4"
              >
                {time}
              </ToggleButton>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between gap-4 pt-4">
          <Button
            type={ButtonType.SECONDARY}
            text="Back"
            icon={<ArrowLeftIcon size={20} />}
            onClick={onBack}
            className="!flex !flex-row-reverse"
          />

          <Button
            type={ButtonType.SECONDARY}
            text="Cancel"
            onClick={onCancel}
          />

          <Button
            type={ButtonType.PRIMARY}
            text="Confirm Appointment"
            onClick={onConfirm}
            disabled={!selectedDateState || !selectedTimeState}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectDateTime;
