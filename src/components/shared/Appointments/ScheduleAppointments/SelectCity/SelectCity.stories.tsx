import type { <PERSON>a, StoryObj } from "@storybook/react";
import SelectCity from "./SelectCity";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";
import { BreadcrumbProvider } from "@/contexts/BreadcrumbContext";

const meta: Meta<typeof SelectCity> = {
  title: "Components/Appointments/ScheduleAppointments/SelectCity",
  component: SelectCity,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A component for selecting a city in the appointment scheduling flow.",
      },
    },
  },
  argTypes: {
    onCitySelect: { action: "city selected" },
    onBack: { action: "back clicked" },
    selectedCityId: {
      control: { type: "select" },
      options: [undefined, 1, 2, 3, 4, 5],
      description: "Pre-selected city ID",
    },
  },
  decorators: [
    (Story) => (
      <PageHeaderProvider>
        <BreadcrumbProvider>
          <div className="w-full max-w-md">
            <Story />
          </div>
        </BreadcrumbProvider>
      </PageHeaderProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SelectCity>;

export const Default: Story = {
  args: {
    selectedCityId: undefined,
  },
};

export const WithPreselectedCity: Story = {
  args: {
    selectedCityId: 1, // Ghaziabad
  },
};

export const WithDifferentPreselectedCity: Story = {
  args: {
    selectedCityId: 3, // Delhi
  },
};

export const Interactive: Story = {
  args: {
    selectedCityId: undefined,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version where you can click on cities to see the selection behavior.",
      },
    },
  },
};
