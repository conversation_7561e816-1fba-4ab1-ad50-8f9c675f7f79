import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import SelectCity from "./SelectCity";

// Mock the useCities hook
jest.mock("@/hooks/useCities", () => ({
  useCities: () => [
    { cityId: 1, city: "Ghaziabad" },
    { cityId: 2, city: "Noida" },
    { cityId: 3, city: "Delhi" },
    { cityId: 4, city: "Meerut" },
    { cityId: 5, city: "Janakpuri" },
  ],
}));

// Mock the StepHeader component
jest.mock("@/components/shared/StepHeader/StepHeader", () => {
  return function MockStepHeader({ title }: { title: string }) {
    return <div data-testid="step-header">{title}</div>;
  };
});

// Mock the Button component
jest.mock("@/components/shared/Button/Button", () => {
  const MockButton = ({
    text,
    onClick,
  }: {
    text: string;
    onClick: () => void;
  }) => {
    return (
      <button data-testid="back-button" onClick={onClick}>
        {text}
      </button>
    );
  };

  MockButton.ButtonType = {
    PRIMARY: "primary",
    SECONDARY: "secondary",
  };

  return MockButton;
});

// Mock the ToggleButton component
jest.mock("@/components/shared/ToggleButton/ToggleButton", () => {
  return function MockToggleButton({
    children,
    isSelected,
    onClick,
  }: {
    children: React.ReactNode;
    isSelected: boolean;
    onClick: () => void;
  }) {
    return (
      <button
        data-testid={`city-button-${children}`}
        data-selected={isSelected}
        onClick={onClick}
      >
        {children}
      </button>
    );
  };
});

describe("SelectCity", () => {
  const mockOnCitySelect = jest.fn();
  const mockOnBack = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with correct title and subtitle", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    expect(screen.getByTestId("step-header")).toHaveTextContent("Select City");
    expect(
      screen.getByText("Choose your city to view available clinics")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Select City", { selector: "label" })
    ).toBeInTheDocument();
  });

  it("renders all cities from the useCities hook", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    expect(screen.getByTestId("city-button-Ghaziabad")).toBeInTheDocument();
    expect(screen.getByTestId("city-button-Noida")).toBeInTheDocument();
    expect(screen.getByTestId("city-button-Delhi")).toBeInTheDocument();
    expect(screen.getByTestId("city-button-Meerut")).toBeInTheDocument();
    expect(screen.getByTestId("city-button-Janakpuri")).toBeInTheDocument();
  });

  it("renders back button", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    expect(screen.getByTestId("back-button")).toBeInTheDocument();
    expect(screen.getByTestId("back-button")).toHaveTextContent("Back");
  });

  it("calls onCitySelect when a city is clicked", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    const ghaziabadButton = screen.getByTestId("city-button-Ghaziabad");
    fireEvent.click(ghaziabadButton);

    expect(mockOnCitySelect).toHaveBeenCalledWith(1, "Ghaziabad");
  });

  it("calls onBack when back button is clicked", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    const backButton = screen.getByTestId("back-button");
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalled();
  });

  it("shows selected city when selectedCityId is provided", () => {
    render(
      <SelectCity
        onCitySelect={mockOnCitySelect}
        onBack={mockOnBack}
        selectedCityId={1}
      />
    );

    const ghaziabadButton = screen.getByTestId("city-button-Ghaziabad");
    expect(ghaziabadButton).toHaveAttribute("data-selected", "true");
  });

  it("shows no selected city when selectedCityId is not provided", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    const allCityButtons = screen.getAllByTestId(/city-button-/);
    allCityButtons.forEach((button) => {
      expect(button).toHaveAttribute("data-selected", "false");
    });
  });

  it("updates selected city when a different city is clicked", async () => {
    render(
      <SelectCity
        onCitySelect={mockOnCitySelect}
        onBack={mockOnBack}
        selectedCityId={1}
      />
    );

    // Initially Ghaziabad should be selected
    expect(screen.getByTestId("city-button-Ghaziabad")).toHaveAttribute(
      "data-selected",
      "true"
    );

    // Click on Delhi
    const delhiButton = screen.getByTestId("city-button-Delhi");
    fireEvent.click(delhiButton);

    await waitFor(() => {
      expect(mockOnCitySelect).toHaveBeenCalledWith(3, "Delhi");
    });
  });

  it("handles multiple city selections correctly", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    // Click on first city
    fireEvent.click(screen.getByTestId("city-button-Ghaziabad"));
    expect(mockOnCitySelect).toHaveBeenCalledWith(1, "Ghaziabad");

    // Click on second city
    fireEvent.click(screen.getByTestId("city-button-Noida"));
    expect(mockOnCitySelect).toHaveBeenCalledWith(2, "Noida");

    // Verify both calls were made
    expect(mockOnCitySelect).toHaveBeenCalledTimes(2);
  });

  it("renders with correct accessibility attributes", () => {
    render(<SelectCity onCitySelect={mockOnCitySelect} onBack={mockOnBack} />);

    // Check that all interactive elements are accessible
    expect(screen.getByTestId("back-button")).toBeInTheDocument();
    expect(screen.getAllByTestId(/city-button-/)).toHaveLength(5);
  });
});
