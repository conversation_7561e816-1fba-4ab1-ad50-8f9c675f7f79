import { Meta, StoryObj } from "@storybook/nextjs";
import AppointmentCard from "./AppointmentCard";

const meta: Meta<typeof AppointmentCard> = {
  title: "Components/Appointments/AppointmentCard",
  component: AppointmentCard,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof AppointmentCard>;

export const Default: Story = {
  args: {
    doctorImageUrl: "https://randomuser.me/api/portraits/women/44.jpg",
    doctorName: "Dr. <PERSON><PERSON><PERSON>",
    clinicName: "Gunjan IVF Clinic",
    clinicLocation: "Vijay Nagar Ghaziabad",
    appointmentDate: "20 July 2025",
    appointmentTime: "03:00 PM - 03:30 PM",
    onReschedule: () => console.log("Reschedule clicked"),
    onCancel: () => console.log("Cancel clicked"),
  },
};
