import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import PatientInfoCard from "./PatientInfoCard";

const meta: Meta<typeof PatientInfoCard> = {
  title: "Components/Appointments/PatientInfoCard",
  component: PatientInfoCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    patientName: {
      control: "text",
      description: "The name of the patient",
    },
    age: {
      control: "text",
      description: "The age of the patient",
    },
    phone: {
      control: "text",
      description: "The phone number of the patient",
    },
    email: {
      control: "text",
      description: "The email address of the patient",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    patientName: "Riya Patel",
    age: "30",
    phone: "90 1223 9880",
    email: "<EMAIL>",
  },
};

export const LongName: Story = {
  args: {
    patientName: "Dr. <PERSON>",
    age: "45",
    phone: "+****************",
    email: "<EMAIL>",
  },
};

export const YoungPatient: Story = {
  args: {
    patientName: "Emma <PERSON>",
    age: "18",
    phone: "98765 43210",
    email: "<EMAIL>",
  },
};

export const SeniorPatient: Story = {
  args: {
    patientName: "Margaret Thompson",
    age: "72",
    phone: "011-2345-6789",
    email: "<EMAIL>",
  },
};

export const InternationalPhone: Story = {
  args: {
    patientName: "Wei Zhang",
    age: "28",
    phone: "+86 138 0013 8000",
    email: "<EMAIL>",
  },
};

export const WithCustomClass: Story = {
  args: {
    patientName: "Sarah Davis",
    age: "35",
    phone: "555-0123",
    email: "<EMAIL>",
    className: "shadow-lg",
  },
};

export const EmptyFields: Story = {
  args: {
    patientName: "",
    age: "",
    phone: "",
    email: "",
  },
};
