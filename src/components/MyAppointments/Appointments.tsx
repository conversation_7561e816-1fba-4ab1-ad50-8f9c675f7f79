import { usePageHeader } from "@/contexts/PageHeaderContext";
import React, { useEffect, useState } from "react";
import NoAppointments from "../shared/Appointments/NoAppointments/NoAppointments";
import { useRouter } from "next/navigation";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

const Appointments: React.FC = () => {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();
  const [appointments, setAppointments] = useState<any[]>([]); // Replace with actual appointments data
  const router = useRouter();

  useEffect(() => {
    setTitle("My Appointments");
    setSubtitle(
      "Get expert answers and connect with others on the same IVF journey."
    );
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  const handleScheduleClick = () => {
    // Handle schedule appointment click
    router.push("/user/schedule-appointments/city");
  };

  // If no appointments, show the NoAppointments component
  if (appointments.length === 0) {
    return (
      <div className="w-full h-[67vh] flex items-center justify-center">
        <NoAppointments onScheduleClick={handleScheduleClick} />
      </div>
    );
  }

  return <div>Appointments</div>;
};

export default Appointments;
