import React, { useState } from "react";
import OTPInput from "../shared/OTPInput/OTPInput";
import { useCountdown } from "@/hooks/useCountdown";
import { ArrowLeft } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/contexts/ToastContext";

interface OTPLoginStepProps {
  email: string;
  onBack: () => void;
  onVerifyOTP: (email: string, otp: string) => Promise<void>;
}

const OTPLoginStep: React.FC<OTPLoginStepProps> = ({
  email,
  onBack,
  onVerifyOTP,
}) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [otp, setOtp] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const countdown = useCountdown(120); // 2 minutes countdown
  const toast = useToast();
  const supabase = createClient();

  const handleOTPChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOTPComplete = async (otpValue: string) => {
    setIsLoading(true);
    setErrorMessage(null);
    try {
      await onVerifyOTP(email, otpValue);
    } catch (error) {
      console.error("OTP verification error:", error);
      setErrorMessage("Failed to verify OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsResending(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (error) {
        setErrorMessage(error.message);
      } else {
        toast.success("Verification code resent successfully!");
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);
      }
    } catch (error) {
      console.error("Resend OTP error:", error);
      setErrorMessage("Failed to resend verification code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="w-[20.063rem] md:w-[26.5rem] flex flex-col justify-center gap-8">
      {/* Back Button */}
      <button
        onClick={onBack}
        className="flex items-center gap-2 text-[var(--grey-6)] hover:text-[var(--grey-7)] transition-colors duration-200 self-start"
      >
        <ArrowLeft size={20} />
        Back to Login
      </button>

      {/* Title */}
      <div className="text-center flex flex-col">
        <h1 className="text-center text-2xl font-semibold text-[var(--grey-7)] mb-2">
          Enter Verification Code
        </h1>
        <p className="text-[var(--grey-6)] text-base font-medium">
          We have sent a 6-digit code to
          <br />
          <span className="font-semibold">{email}</span>
        </p>
      </div>

      {/* OTP Input */}
      <div className="space-y-6">
        <OTPInput
          length={6}
          onChange={handleOTPChange}
          onComplete={handleOTPComplete}
          disabled={isLoading}
          error={!!errorMessage}
          autoFocus
        />

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="text-[var(--green-6)] text-sm text-center bg-green-50 border border-green-200 rounded-sm p-3">
            Verification code sent successfully!
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
            {errorMessage}
          </div>
        )}

        {/* Resend Code Section */}
        <div className="text-center">
          <p className="text-[var(--grey-5)] text-sm mb-2">
            Didn&apos;t receive the code?
          </p>
          
          {countdown.seconds > 0 ? (
            <p className="text-[var(--grey-5)] text-sm">
              Resend code in {Math.floor(countdown.seconds / 60)}:
              {(countdown.seconds % 60).toString().padStart(2, "0")}
            </p>
          ) : (
            <button
              type="button"
              onClick={handleResendCode}
              disabled={isResending}
              className="text-[var(--red-6)] font-medium hover:text-[var(--red-7)] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? "Sending..." : "Resend Code"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OTPLoginStep;
