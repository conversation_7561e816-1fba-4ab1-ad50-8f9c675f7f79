import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch clinics with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const search = searchParams.get("search") || "";
    const cityId = searchParams.get("city_id");

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (search) {
      where.OR = [
        {
          clinic_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          address: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          contact_info: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    if (cityId) {
      where.city_id = parseInt(cityId);
    }

    const [clinics, total] = await Promise.all([
      prisma.clinics.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          city: {
            select: {
              city_name: true,
              state_name: true
            }
          },
          _count: {
            select: {
              doctors: true,
              appointments: true
            }
          }
        },
        orderBy: { clinic_name: 'asc' }
      }),
      prisma.clinics.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        clinics: serializePrismaResponse(clinics),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching clinics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch clinics' },
      { status: 500 }
    );
  }
}

// POST - Create a new clinic
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clinicName,
      address,
      cityId,
      contactInfo,
      latitude,
      longitude
    } = body;

    if (!clinicName) {
      return NextResponse.json(
        { success: false, error: 'Clinic name is required' },
        { status: 400 }
      );
    }

    // Validate city exists if provided
    if (cityId) {
      const city = await prisma.cities.findUnique({
        where: { id: parseInt(cityId) }
      });

      if (!city) {
        return NextResponse.json(
          { success: false, error: 'Invalid city ID' },
          { status: 400 }
        );
      }
    }

    const clinic = await prisma.clinics.create({
      data: {
        clinic_name: clinicName,
        address,
        city_id: cityId ? parseInt(cityId) : null,
        contact_info: contactInfo,
        latitude: latitude ? parseFloat(latitude) : null,
        longitude: longitude ? parseFloat(longitude) : null
      },
      include: {
        city: {
          select: {
            city_name: true,
            state_name: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(clinic)
    });
  } catch (error) {
    console.error('Error creating clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create clinic' },
      { status: 500 }
    );
  }
}
