"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import SelectDateTime from "@/components/shared/Appointments/ScheduleAppointments/SelectDateTime/SelectDateTime";

const DateTimeSelectionPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedDate, setSelectedDate] = useState<string | undefined>();
  const [selectedTime, setSelectedTime] = useState<string | undefined>();

  // Get all appointment data from URL parameters
  const cityId = searchParams.get("cityId");
  const cityName = searchParams.get("cityName");
  const clinicId = searchParams.get("clinicId");
  const clinicName = searchParams.get("clinicName");
  const doctorId = searchParams.get("doctorId");
  const doctorName = searchParams.get("doctorName");

  useEffect(() => {
    // Redirect if no required data is provided
    if (
      !cityId ||
      !cityName ||
      !clinicId ||
      !clinicName ||
      !doctorId ||
      !doctorName
    ) {
      router.push("/user/schedule-appointments/city");
    }
  }, [cityId, cityName, clinicId, clinicName, doctorId, doctorName, router]);

  const handleDateTimeSelect = (date: string, time: string) => {
    setSelectedDate(date);
    setSelectedTime(time);
  };

  const handleBack = () => {
    // Navigate back to doctor selection page with all previous data
    router.push(
      `/user/schedule-appointments/doctor?cityId=${cityId}&cityName=${encodeURIComponent(cityName || "")}&clinicId=${clinicId}&clinicName=${encodeURIComponent(clinicName || "")}`
    );
  };

  const handleCancel = () => {
    // Navigate back to appointments page
    router.push("/user/appointments");
  };

  const handleConfirm = () => {
    if (!selectedDate || !selectedTime) {
      return;
    }

    // Navigate to appointment confirmation page with all data
    router.push(
      `/user/schedule-appointments/confirmation?cityId=${cityId}&cityName=${encodeURIComponent(cityName || "")}&clinicId=${clinicId}&clinicName=${encodeURIComponent(clinicName || "")}&doctorId=${doctorId}&doctorName=${encodeURIComponent(doctorName || "")}&date=${selectedDate}&time=${encodeURIComponent(selectedTime)}`
    );
  };

  // Don't render if no required data
  if (
    !cityId ||
    !cityName ||
    !clinicId ||
    !clinicName ||
    !doctorId ||
    !doctorName
  ) {
    return null;
  }

  return (
    <div className="w-full h-full flex items-start justify-center py-25">
      <SelectDateTime
        onDateTimeSelect={handleDateTimeSelect}
        onBack={handleBack}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        selectedDate={selectedDate}
        selectedTime={selectedTime}
      />
    </div>
  );
};

export default DateTimeSelectionPage;
