"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import SelectCity from "@/components/shared/Appointments/ScheduleAppointments/SelectCity/SelectCity";

const CitySelectionPage: React.FC = () => {
  const router = useRouter();
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();
  const [selectedCityName, setSelectedCityName] = useState<string>("");

  const handleCitySelect = (cityId: number, cityName: string) => {
    setSelectedCityId(cityId);
    setSelectedCityName(cityName);

    // Navigate to clinic selection page with city data
    router.push(
      `/user/schedule-appointments/clinic?cityId=${cityId}&cityName=${encodeURIComponent(cityName)}`
    );
  };

  const handleBack = () => {
    // Navigate back to the previous page or home
    router.push("/user/appointments");
  };

  return (
    <div className="w-full h-full flex items-start justify-center py-25">
      <SelectCity
        onCitySelect={handleCitySelect}
        onBack={handleBack}
        selectedCityId={selectedCityId}
      />
    </div>
  );
};

export default CitySelectionPage;
