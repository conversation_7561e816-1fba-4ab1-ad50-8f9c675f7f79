"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import SelectClinic from "@/components/shared/Appointments/ScheduleAppointments/SelectClinic/SelectClinic";

const ClinicSelectionPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedClinicId, setSelectedClinicId] = useState<
    number | undefined
  >();
  const [selectedClinicName, setSelectedClinicName] = useState<string>("");

  // Get city data from URL parameters
  const cityId = searchParams.get("cityId");
  const cityName = searchParams.get("cityName");

  useEffect(() => {
    // Redirect if no city data is provided
    if (!cityId || !cityName) {
      router.push("/user/schedule-appointments/city");
    }
  }, [cityId, cityName, router]);

  const handleClinicSelect = (clinicId: number, clinicName: string) => {
    setSelectedClinicId(clinicId);
    setSelectedClinicName(clinicName);

    // Navigate to doctor selection page with city and clinic data
    router.push(
      `/user/schedule-appointments/doctor?cityId=${cityId}&cityName=${encodeURIComponent(cityName || "")}&clinicId=${clinicId}&clinicName=${encodeURIComponent(clinicName)}`
    );
  };

  const handleBack = () => {
    // Navigate back to city selection page
    router.push("/user/schedule-appointments/city");
  };

  // Don't render if no city data
  if (!cityId || !cityName) {
    return null;
  }

  return (
    <div className="w-full h-full flex items-start justify-center py-25">
      <SelectClinic
        cityId={parseInt(cityId)}
        onClinicSelect={handleClinicSelect}
        onBack={handleBack}
        selectedClinicId={selectedClinicId}
      />
    </div>
  );
};

export default ClinicSelectionPage;
