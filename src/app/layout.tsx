import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext"; // Add this import
import { cookies } from "next/headers";
import { StagingAuthForm } from "./staging-auth/form";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  /**------------ This is a temporary code to protect the staging site -----------------*/
  const isStaging = process.env.APP_ENV === "staging";
  let allow = true;
  if (isStaging) {
    const stagingPassword = process.env.STAGING_PASSWORD;
    const cookieStore = await cookies();
    const password = cookieStore.get("staging_password")?.value;
    allow = stagingPassword === password;
    console.log("App is using ---- STAGING DB ----");
  } else {
    console.log("App is using ---- PRODUCTION DB ----");
  }

  if (!allow) {
    return (
      <html lang="en" suppressHydrationWarning>
        <head>
          <link
            href="https://api.fontshare.com/v2/css?f[]=satoshi@400&display=swap"
            rel="stylesheet"
          />
        </head>
        <body className={`${geistSans.variable} ${geistMono.variable}`}>
          <StagingAuthForm />
        </body>
      </html>
    );
  }
  /**------------ This is a temporary code to protect the staging site -----------------*/

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link
          href="https://api.fontshare.com/v2/css?f[]=satoshi@400&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        {process.env.APP_ENV === "staging" ? (
          <div className="text-sm bg-yellow-200 p-2 rounded z-90 fixed top-0 left-0">
            Staging Site
          </div>
        ) : null}
        <QueryProvider>
          <ToastProvider duration={4000} maxToasts={5}>
            <AuthProvider>{children}</AuthProvider>
          </ToastProvider>
        </QueryProvider>
        {/* Remove the Sonner Toaster component */}
      </body>
    </html>
  );
}
