"use client";
import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ShadcnUI/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import { Label } from "@/components/ShadcnUI/label";
import { Badge } from "@/components/ShadcnUI/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Search, Plus, Edit, DollarSign, Clock, Calendar, User } from "lucide-react";

interface Doctor {
  id: number;
  profile: {
    display_name: string;
    email: string;
  };
  specialization_name?: string;
}

interface TimeSlot {
  id: number;
  date: string;
  start_time: string;
  end_time: string;
  duration: number;
  fee?: {
    s: number;
    e: number;
    d: number[];
  } | number;
  currency?: string;
  is_available: boolean;
}

interface FeeSettings {
  doctorId: number;
  defaultFee: number;
  currency: string;
  consultationType: "in_person" | "online";
}

export default function FeeManagementPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { user } = useAuth();
  const [currentDoctor, setCurrentDoctor] = useState<Doctor | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSlot, setEditingSlot] = useState<TimeSlot | null>(null);
  const [formData, setFormData] = useState({
    fee: "",
    currency: "USD",
  });

  useEffect(() => {
    setTitle("Fee Management");
    setSubtitle("Manage your appointment fees and pricing");
  }, [setTitle, setSubtitle]);

  useEffect(() => {
    fetchCurrentDoctor();
  }, [user]);

  useEffect(() => {
    if (currentDoctor) {
      fetchTimeSlots();
    }
  }, [currentDoctor]);

  const fetchCurrentDoctor = async () => {
    if (!user?.id) return;

    try {
      // Fetch current doctor's profile
      const response = await fetch(`/api/v1/admin/doctors/${user.id}`);
      const data = await response.json();

      if (data.success) {
        setCurrentDoctor(data.data);
      } else {
        // If not found as doctor, show error or redirect
        console.error("User is not a doctor");
      }
    } catch (error) {
      console.error("Error fetching doctor profile:", error);
    }
  };

  const fetchTimeSlots = async () => {
    if (!currentDoctor?.id) return;

    try {
      setLoading(true);
      const params = new URLSearchParams({
        doctor_id: currentDoctor.id.toString(),
        per_page: "50",
      });

      const response = await fetch(`/api/v1/admin/time-slots?${params}`);
      const data = await response.json();

      if (data.success) {
        setTimeSlots(data.data);
      }
    } catch (error) {
      console.error("Error fetching time slots:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentDoctor?.id || !formData.fee) return;

    try {
      const response = await fetch("/api/v1/admin/time-slots", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          updates: [{
            id: editingSlot?.id,
            fee: parseFloat(formData.fee),
            currency: formData.currency,
          }],
        }),
      });

      if (response.ok) {
        setIsDialogOpen(false);
        setEditingSlot(null);
        setFormData({ fee: "", currency: "USD" });
        fetchTimeSlots();
      }
    } catch (error) {
      console.error("Error updating fee:", error);
    }
  };

  const handleEdit = (slot: TimeSlot) => {
    setEditingSlot(slot);
    setFormData({
      fee: getFeeValue(slot.fee).toString() || "",
      currency: slot.currency || "USD",
    });
    setIsDialogOpen(true);
  };

  const handleBulkUpdate = async (fee: number, currency: string) => {
    if (!currentDoctor?.id) return;

    try {
      const updates = timeSlots.map(slot => ({
        id: slot.id,
        fee: fee,
        currency: currency,
      }));

      const response = await fetch("/api/v1/admin/time-slots", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      });

      if (response.ok) {
        fetchTimeSlots();
      }
    } catch (error) {
      console.error("Error updating fees:", error);
    }
  };

  const getCurrencySymbol = (currency: string) => {
    const symbols: Record<string, string> = {
      USD: "$",
      EUR: "€",
      GBP: "£",
      INR: "₹",
    };
    return symbols[currency] || currency;
  };

  const getFeeValue = (fee: TimeSlot['fee']): number => {
    if (typeof fee === 'number') {
      return fee;
    }
    if (fee && typeof fee === 'object' && 's' in fee) {
      // Extract the fee value from the object structure
      // Based on the API response, this seems to be a fee structure
      return fee.s || 0;
    }
    return 0;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          {currentDoctor ? (
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <User className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-medium text-blue-900">
                  {currentDoctor.profile.display_name}
                </h3>
                {currentDoctor.specialization_name && (
                  <p className="text-sm text-blue-700">
                    {currentDoctor.specialization_name}
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="text-gray-500">Loading doctor profile...</div>
          )}
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button disabled={!currentDoctor}>
              <Plus className="h-4 w-4 mr-2" />
              Set Default Fee
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingSlot ? "Update Time Slot Fee" : "Set Default Fee"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="fee">Fee Amount *</Label>
                <Input
                  id="fee"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.fee}
                  onChange={(e) =>
                    setFormData({ ...formData, fee: e.target.value })
                  }
                  required
                />
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={formData.currency}
                  onValueChange={(value) =>
                    setFormData({ ...formData, currency: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD ($)</SelectItem>
                    <SelectItem value="EUR">EUR (€)</SelectItem>
                    <SelectItem value="GBP">GBP (£)</SelectItem>
                    <SelectItem value="INR">INR (₹)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {editingSlot ? "Update" : "Set Default"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Summary Cards */}
      {currentDoctor && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Time Slots</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{timeSlots.length}</div>
              <p className="text-xs text-muted-foreground">
                Available and unavailable slots
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Slots with Fees</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {timeSlots.filter(slot => slot.fee).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Slots with custom pricing
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Fee</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(() => {
                  const slotsWithFees = timeSlots.filter(slot => slot.fee);
                  if (slotsWithFees.length === 0) return "N/A";
                  const avgFee = slotsWithFees.reduce((sum, slot) => sum + getFeeValue(slot.fee), 0) / slotsWithFees.length;
                  return `$${avgFee.toFixed(2)}`;
                })()}
              </div>
              <p className="text-xs text-muted-foreground">
                Average fee across all slots
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Time Slots Table */}
      {currentDoctor && (
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-4 border-b">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Time Slot Fees</h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkUpdate(100, "USD")}
                >
                  Set All to $100
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkUpdate(150, "USD")}
                >
                  Set All to $150
                </Button>
              </div>
            </div>
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Fee</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {timeSlots.map((slot) => (
                <TableRow key={slot.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {formatDate(slot.date)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                    </div>
                  </TableCell>
                  <TableCell>{slot.duration} min</TableCell>
                  <TableCell>
                    {slot.fee ? (
                      <div className="flex items-center gap-1">
                        <span className="font-medium">
                          {getCurrencySymbol(slot.currency || "USD")}{getFeeValue(slot.fee)}
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {slot.currency || "USD"}
                        </Badge>
                      </div>
                    ) : (
                      <span className="text-gray-400">Not set</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={slot.is_available ? "default" : "secondary"}>
                      {slot.is_available ? "Available" : "Unavailable"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(slot)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Instructions */}
      {!currentDoctor && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-medium text-blue-900">Fee Management</h3>
          </div>
          <p className="text-blue-700">
            Loading your doctor profile... You can manage your appointment fees, 
            customize individual time slot pricing, and bulk update fees for multiple slots.
          </p>
        </div>
      )}
    </div>
  );
}
