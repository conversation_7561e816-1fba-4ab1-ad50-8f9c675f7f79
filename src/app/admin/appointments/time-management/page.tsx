"use client";
import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ShadcnUI/button";
import { Calendar, ChevronLeft, ChevronRight, AlertCircle } from "lucide-react";
import { format, startOfWeek, addDays, addMinutes, isSameDay, parseISO } from "date-fns";

interface TimeSlot {
  id: number;
  date: string;
  start_time: string;
  end_time: string;
  duration: number;
  is_available: boolean;
  fee?: {
    s: number;
    e: number;
    d: number[];
  } | number;
  currency?: string;
}

interface CurrentDoctor {
  id: number;
  profile: {
    display_name: string;
    email: string;
  };
}

export default function TimeManagementPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { user } = useAuth();
  const [currentDoctor, setCurrentDoctor] = useState<CurrentDoctor | null>(null);
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    setTitle("Time Management");
    setSubtitle("Manage doctor availability");
  }, [setTitle, setSubtitle]);

  useEffect(() => {
    fetchCurrentDoctor();
  }, [user]);

  useEffect(() => {
    if (currentDoctor) {
      fetchTimeSlots();
    }
  }, [currentDoctor, currentWeek]);

  const fetchCurrentDoctor = async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/v1/admin/doctors/${user.id}`);
      const data = await response.json();

      if (data.success) {
        setCurrentDoctor(data.data);
      }
    } catch (error) {
      console.error("Error fetching current doctor:", error);
      setMessage({ type: 'error', text: 'Failed to load doctor information' });
    }
  };

  const fetchTimeSlots = async () => {
    if (!currentDoctor) return;

    try {
      setLoading(true);
      const startDate = startOfWeek(currentWeek);
      const endDate = addDays(startDate, 6);

      const params = new URLSearchParams({
        doctor_id: currentDoctor.id.toString(),
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
      });

      const response = await fetch(`/api/v1/admin/time-slots?${params}`);
      const data = await response.json();

      if (data.success) {
        setTimeSlots(data.data);
      }
    } catch (error) {
      console.error("Error fetching time slots:", error);
      setMessage({ type: 'error', text: 'Failed to load time slots' });
    } finally {
      setLoading(false);
    }
  };

  const generateTimeSlots = () => {
    const slots = [];
    const startTime = new Date();
    startTime.setHours(9, 0, 0, 0); // 9 AM
    const endTime = new Date();
    endTime.setHours(17, 0, 0, 0); // 5 PM

    let currentTime = new Date(startTime);
    while (currentTime < endTime) {
      slots.push(format(currentTime, "HH:mm"));
      currentTime = addMinutes(currentTime, 15);
    }

    return slots;
  };

  const getWeekDays = () => {
    const start = startOfWeek(currentWeek);
    const days = [];
    
    for (let i = 0; i < 7; i++) {
      days.push(addDays(start, i));
    }
    
    return days;
  };

  const getSlotForDayAndTime = (date: Date, time: string) => {
    const daySlots = timeSlots.filter(slot => 
      isSameDay(parseISO(slot.date), date) && 
      format(parseISO(slot.start_time), "HH:mm") === time
    );
    
    return daySlots[0];
  };

  const toggleSlotAvailability = async (date: Date, time: string) => {
    if (!currentDoctor) return;

    try {
      const existingSlot = getSlotForDayAndTime(date, time);

      if (existingSlot) {
        // If slot exists and is available, delete it (make unavailable)
        const response = await fetch(`/api/v1/admin/time-slots/${existingSlot.id}`, {
          method: "DELETE",
        });

        if (response.ok) {
          setMessage({ type: 'success', text: 'Time slot made unavailable' });
          fetchTimeSlots();
        } else {
          setMessage({ type: 'error', text: 'Failed to update time slot' });
        }
      } else {
        // If slot doesn't exist, create it as available
        const [hours, minutes] = time.split(':').map(Number);
        const startTime = new Date(date);
        startTime.setHours(hours, minutes, 0, 0);
        const endTime = new Date(startTime);
        endTime.setMinutes(endTime.getMinutes() + 15);

        const response = await fetch("/api/v1/admin/time-slots", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            doctorId: currentDoctor.id,
            slots: [{
              date: date.toISOString(),
              startTime: startTime.toISOString(),
              endTime: endTime.toISOString(),
              duration: 15,
              isAvailable: true
            }]
          }),
        });

        if (response.ok) {
          setMessage({ type: 'success', text: 'Time slot made available' });
          fetchTimeSlots();
        } else {
          setMessage({ type: 'error', text: 'Failed to create time slot' });
        }
      }
    } catch (error) {
      console.error("Error toggling time slot:", error);
      setMessage({ type: 'error', text: 'Failed to toggle time slot' });
    }
  };

  const navigateWeek = (direction: "prev" | "next") => {
    setCurrentWeek(prev => {
      const newDate = new Date(prev);
      if (direction === "prev") {
        newDate.setDate(newDate.getDate() - 7);
      } else {
        newDate.setDate(newDate.getDate() + 7);
      }
      return newDate;
    });
  };

  const timeSlotsList = generateTimeSlots();
  const weekDays = getWeekDays();

  // Clear message after 5 seconds
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg flex items-center gap-2 ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <AlertCircle className="h-4 w-4" />
          {message.text}
        </div>
      )}

      {/* Controls */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Current Doctor Info */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Calendar className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="font-medium">
                {currentDoctor?.profile.display_name || "Loading..."}
              </div>
              <div className="text-sm text-gray-500">
                {currentDoctor?.profile.email || ""}
              </div>
            </div>
          </div>

          {/* Week Navigation */}
          <div className="flex items-center gap-2 justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateWeek("prev")}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium">
              {format(weekDays[0], "MMM dd")} - {format(weekDays[6], "MMM dd, yyyy")}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateWeek("next")}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      {currentDoctor && (
        <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
          <div className="min-w-[800px]">
            {/* Header */}
            <div className="grid grid-cols-8 border-b">
              <div className="p-4 font-medium">Time</div>
              {weekDays.map((day) => (
                <div key={day.toISOString()} className="p-4 text-center font-medium border-l">
                  <div>{format(day, "EEE")}</div>
                  <div className="text-sm text-gray-500">{format(day, "MMM dd")}</div>
                </div>
              ))}
            </div>

            {/* Time Slots */}
            {timeSlotsList.map((time) => (
              <div key={time} className="grid grid-cols-8 border-b">
                <div className="p-4 text-sm text-gray-500 border-r">
                  {time}
                </div>
                {weekDays.map((day) => {
                  const slot = getSlotForDayAndTime(day, time);
                  const isAvailable = slot && slot.is_available;

                  return (
                    <div
                      key={`${day.toISOString()}-${time}`}
                      className="p-2 border-l min-h-[60px] flex items-center justify-center"
                    >
                      <div
                        className={`w-full h-full rounded cursor-pointer transition-colors ${
                          isAvailable
                            ? "bg-blue-100 hover:bg-blue-200"
                            : "bg-gray-100 hover:bg-gray-200"
                        }`}
                        onClick={() => toggleSlotAvailability(day, time)}
                      >
                        <div className="text-xs text-center flex items-center justify-center h-full">
                          <div className="font-medium">
                            {isAvailable ? "Available" : "Unavailable"}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center gap-6 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-100 rounded"></div>
            <span>Available</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gray-100 rounded"></div>
            <span>Unavailable</span>
          </div>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          Click on any time slot to toggle availability. Blue indicates available slots, gray indicates unavailable slots.
        </div>
      </div>
    </div>
  );
}
