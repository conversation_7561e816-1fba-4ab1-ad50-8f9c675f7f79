import { createClient } from "@/utils/supabase/client";
import { UserWithRole } from "@/contexts/AuthContext";

/**
 * Check if the current user has admin role
 * @returns Promise<boolean> - true if user has admin role, false otherwise
 */
export async function checkAdminRole(): Promise<boolean> {
  try {
    const supabase = createClient();
    const { data, error } = await supabase.rpc('authorize', {
      resource_action: 'admin.access'
    });
    
    if (error) {
      console.error('Error checking admin role:', error);
      return false;
    }
    
    return !!data;
  } catch (error) {
    console.error('Error checking admin role:', error);
    return false;
  }
} 



// Define which menu items should be hidden for specific roles
const HIDDEN_MENUS_BY_ROLE: Record<string, string[]> = {
  doctor: [
    "Roles & Permissions",
    "Users", 
    "Settings",
    "Clinics",
    "Cities"
  ]
};

/**
 * Check if a menu item should be hidden for the current user role
 * @param menuTitle - The title of the menu item
 * @param user - The current user with role information
 * @returns boolean - true if the menu should be hidden, false if it should be shown
 */
export function shouldHideMenuItem(menuTitle: string, user: UserWithRole | null): boolean {
  if (!user || !user.user_role) {
    return false; // Show all menus if no role is set
  }

  const hiddenMenus = HIDDEN_MENUS_BY_ROLE[user.user_role];
  return hiddenMenus ? hiddenMenus.includes(menuTitle) : false;
}

/**
 * Filter sidebar items based on user role
 * @param items - Array of sidebar items
 * @param user - The current user with role information
 * @returns Array of filtered sidebar items
 */
export function filterSidebarItems(items: any[], user: UserWithRole | null): any[] {
  
  return items.map(item => {
    // Check if main item should be hidden
    if (shouldHideMenuItem(item.title, user)) {
      return null;
    }

    // If item has subItems, filter them too
    if (item.subItems) {
      const filteredSubItems = item.subItems.filter((subItem: any) => !shouldHideMenuItem(subItem.title, user));

      // If all subItems are hidden, hide the parent item too
      if (filteredSubItems.length === 0) {
        return null;
      }
      
      // Return item with filtered subItems
      return {
        ...item,
        subItems: filteredSubItems
      };
    }

    return item;
  }).filter(item => item !== null);
} 