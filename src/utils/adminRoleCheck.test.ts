import { shouldHideMenuItem, filterSidebarItems, checkAdminRole } from './adminRoleCheck';
import { UserWithRole } from '@/contexts/AuthContext';
import { createClient } from '@/utils/supabase/client';

// Mock the Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(),
}));

describe('adminRoleCheck', () => {
  const mockAdminUser: UserWithRole = {
    id: '1',
    user_role: 'admin',
  } as UserWithRole;

  const mockDoctorUser: UserWithRole = {
    id: '2',
    user_role: 'doctor',
  } as UserWithRole;

  const mockUserWithoutRole: UserWithRole = {
    id: '3',
    user_role: null,
  } as UserWithRole;

  const mockSupabase = {
    rpc: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('checkAdminRole', () => {
    it('should return true when user has admin role', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null,
      });

      const result = await checkAdminRole();
      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('authorize', {
        resource_action: 'admin.access',
      });
    });

    it('should return false when user does not have admin role', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: false,
        error: null,
      });

      const result = await checkAdminRole();
      expect(result).toBe(false);
    });

    it('should return false when there is an error', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      });

      const result = await checkAdminRole();
      expect(result).toBe(false);
    });
  });

  describe('shouldHideMenuItem', () => {
    it('should return false for admin users (show all menus)', () => {
      expect(shouldHideMenuItem('Roles & Permissions', mockAdminUser)).toBe(false);
      expect(shouldHideMenuItem('Users', mockAdminUser)).toBe(false);
      expect(shouldHideMenuItem('Settings', mockAdminUser)).toBe(false);
      expect(shouldHideMenuItem('Clinics', mockAdminUser)).toBe(false);
      expect(shouldHideMenuItem('Cities', mockAdminUser)).toBe(false);
    });

    it('should return true for doctor users for restricted menus', () => {
      expect(shouldHideMenuItem('Roles & Permissions', mockDoctorUser)).toBe(true);
      expect(shouldHideMenuItem('Users', mockDoctorUser)).toBe(true);
      expect(shouldHideMenuItem('Settings', mockDoctorUser)).toBe(true);
      expect(shouldHideMenuItem('Clinics', mockDoctorUser)).toBe(true);
      expect(shouldHideMenuItem('Cities', mockDoctorUser)).toBe(true);
    });

    it('should return false for doctor users for allowed menus', () => {
      expect(shouldHideMenuItem('Dashboard', mockDoctorUser)).toBe(false);
      expect(shouldHideMenuItem('IVF', mockDoctorUser)).toBe(false);
      expect(shouldHideMenuItem('Diet Plan', mockDoctorUser)).toBe(false);
      expect(shouldHideMenuItem('Appointments', mockDoctorUser)).toBe(false);
    });

    it('should return false for users without role (show all menus)', () => {
      expect(shouldHideMenuItem('Roles & Permissions', mockUserWithoutRole)).toBe(false);
      expect(shouldHideMenuItem('Users', mockUserWithoutRole)).toBe(false);
    });

    it('should return false for null user', () => {
      expect(shouldHideMenuItem('Roles & Permissions', null)).toBe(false);
    });
  });

  describe('filterSidebarItems', () => {
    const mockSidebarItems = [
      {
        title: 'Dashboard',
        pathname: '/admin',
      },
      {
        title: 'Roles & Permissions',
        pathname: '/admin/roles-permissions',
      },
      {
        title: 'Users',
        pathname: '/admin/users',
      },
      {
        title: 'Appointments',
        subItems: [
          {
            title: 'All Appointments',
            pathname: '/admin/appointments',
          },
          {
            title: 'Clinics',
            pathname: '/admin/appointments/clinics',
          },
          {
            title: 'Cities',
            pathname: '/admin/appointments/cities',
          },
        ],
      },
    ];

    it('should return all items for admin users', () => {
      const result = filterSidebarItems(mockSidebarItems, mockAdminUser);
      expect(result).toHaveLength(4);
      expect(result[0].title).toBe('Dashboard');
      expect(result[1].title).toBe('Roles & Permissions');
      expect(result[2].title).toBe('Users');
      expect(result[3].title).toBe('Appointments');
    });

    it('should filter out restricted items for doctor users', () => {
      const result = filterSidebarItems(mockSidebarItems, mockDoctorUser);
      expect(result).toHaveLength(2);
      expect(result[0].title).toBe('Dashboard');
      expect(result[1].title).toBe('Appointments');
      
      // Check that subItems are also filtered
      expect(result[1].subItems).toHaveLength(1);
      expect(result[1].subItems[0].title).toBe('All Appointments');
    });

    it('should return all items for users without role', () => {
      const result = filterSidebarItems(mockSidebarItems, mockUserWithoutRole);
      expect(result).toHaveLength(4);
    });

    it('should return all items for null user', () => {
      const result = filterSidebarItems(mockSidebarItems, null);
      expect(result).toHaveLength(4);
    });

    it('should hide parent item if all subItems are filtered out', () => {
      const itemsWithOnlyRestrictedSubItems = [
        {
          title: 'Appointments',
          subItems: [
            {
              title: 'Clinics',
              pathname: '/admin/appointments/clinics',
            },
            {
              title: 'Cities',
              pathname: '/admin/appointments/cities',
            },
          ],
        },
      ];

      const result = filterSidebarItems(itemsWithOnlyRestrictedSubItems, mockDoctorUser);
      expect(result).toHaveLength(0);
    });

    // Specific test for the actual appointments menu structure
    it('should properly filter appointments submenu for doctor users', () => {
      const appointmentsMenu = [
        {
          title: 'Appointments',
          subItems: [
            {
              title: 'All Appointments',
              pathname: '/admin/appointments',
            },
            {
              title: 'Time Management',
              pathname: '/admin/appointments/time-management',
            },
            {
              title: 'Fee Management',
              pathname: '/admin/appointments/fee-management',
            },
            {
              title: 'Clinics',
              pathname: '/admin/appointments/clinics',
            },
            {
              title: 'Cities',
              pathname: '/admin/appointments/cities',
            },
          ],
        },
      ];

      const result = filterSidebarItems(appointmentsMenu, mockDoctorUser);
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Appointments');
      expect(result[0].subItems).toHaveLength(3);
      expect(result[0].subItems.map((item: any) => item.title)).toEqual([
        'All Appointments',
        'Time Management',
        'Fee Management'
      ]);
    });
  });
}); 